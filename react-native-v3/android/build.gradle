buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 28
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

allprojects {
    repositories {
        // Other repositories
        maven {
            url 'https://gitlab.com/api/v4/projects/43396247/packages/maven'
        }
    }
}

apply plugin: "com.facebook.react.rootproject"
